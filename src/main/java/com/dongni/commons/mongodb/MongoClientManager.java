package com.dongni.commons.mongodb;

import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCompressor;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.DropIndexOptions;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.connection.ConnectionPoolSettings;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toMap;

/**
 * MongoDB数据库管理类
 * Created by 马腾 on 2015/11/19.
 * 继承该类
 * 复制下面两行注释掉的注解，填写好自己配置的前缀
 */
//@Component
//@ConditionalOnProperty("spring.data.mongodb.basedata")
public class MongoClientManager {

    private final Logger log = LogManager.getLogger(this.getClass().getName());

    // Mongodb客户端，自带连接池
    private volatile MongoClient mongoClient = null;

    // 配置 任选
    private String host;
    private String port;

    private String host1;
    private String port1;

    private String host2;
    private String port2;

    // 通用
    private String username;
    private String password;
    private String database;

    public void setHost(String host) {
        this.host = host;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public void setHost1(String host1) {
        this.host1 = host1;
    }

    public void setHost2(String host2) {
        this.host2 = host2;
    }

    public void setPort1(String port1) {
        this.port1 = port1;
    }

    public void setPort2(String port2) {
        this.port2 = port2;
    }

    public void setUserName(String username) {
        this.username = username;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    /**
     * 获得MongoClient数据库，因为MongoClient自带连接池，所以一个JVM只需获取一次MongoClient
     *
     * @return MongoClient mongodb客户端
     */
    public MongoClient getMongoClient() {
        if (this.mongoClient == null) {
            synchronized (this) {
                if (this.mongoClient == null) {
                    // 以 配置的port为判断条件，如果port是数字，则视为有效条件进行配置，
                    // 不校验端口范围等，使用者自行注意
                    int hostCount = 0;
                    if (NumberUtils.isNumber(this.port))  { hostCount++; }
                    if (NumberUtils.isNumber(this.port1)) { hostCount++; }
                    if (NumberUtils.isNumber(this.port2)) { hostCount++; }
    
                    // mongodb配置List
                    List<ServerAddress> serverAddressList = new ArrayList<>(hostCount);
                    List<String> logInfoOfHost = new ArrayList<>(hostCount);
    
                    // 设置 serverAddressList 及 logInfoOfHost
                    setServerAddressAndLogInfo(serverAddressList, logInfoOfHost, host, port);
                    setServerAddressAndLogInfo(serverAddressList, logInfoOfHost, host1, port1);
                    setServerAddressAndLogInfo(serverAddressList, logInfoOfHost, host2, port2);
    
                    log.info("初始化mongodb:{}@{}", this.database, StringUtils.join(logInfoOfHost, "; "));
    
                    // 创建链接
                    MongoCredential mongoCredential = MongoCredential
                            .createCredential(this.username, this.database, this.password.toCharArray());
                    this.mongoClient = MongoClients.create(
                            MongoClientSettings.builder()
                                    .credential(mongoCredential)
                                    .compressorList(Collections.singletonList(MongoCompressor.createSnappyCompressor()))
                                    .applyToConnectionPoolSettings(builder -> {
                                        builder
                                                .maxConnectionIdleTime(3, TimeUnit.MINUTES)
                                                .maxConnectionLifeTime(5, TimeUnit.MINUTES)
                                        ;
                                        connectionPoolSettings(builder);
                                    })
                                    .applyToClusterSettings(builder -> builder.hosts(serverAddressList))
                                    .build()
                    );
                }
            }
        }
        return this.mongoClient;
    }
    
    /**
     * 获得MongoClient数据库，因为MongoClient自带连接池，所以一个JVM只需获取一次MongoClient
     * @return MongoClient mongodb客户端
     */
    public MongoDatabase getMongoDatabase() {
        return getMongoClient().getDatabase(this.database);
    }
    
    /**
     * 配置链接池的信息
     * @param connectionPoolSettingsBuilder 链接池设置builder
     */
    protected void connectionPoolSettings(ConnectionPoolSettings.Builder connectionPoolSettingsBuilder) {
        // connectionPoolSettingsBuilder.maxSize(100);  // 示例代码 设置链接池的大小 默认为100
    }

    /**
     * 设置配置信息及日志信息
     * @param serverAddressList 配置list，存储 ServerAddress.class 实例
     * @param logInfoOfHost     配置log信息，存储 host:port
     * @param host              配置地址
     * @param port              端口，如果该参数为数字，则设置，如果不是则忽略
     */
    private void setServerAddressAndLogInfo(List<ServerAddress> serverAddressList, List<String> logInfoOfHost, String host, String port) {
        if (NumberUtils.isNumber(port)) {
            serverAddressList.add(new ServerAddress(host, Integer.parseInt(port)));
            logInfoOfHost.add(host + ":" + port);
        }
    }
    
    /**
     * 初始化索引
     *   如果集合中没有名称为所给的索引则创建，如果已经存在则忽略
     *  *** 注意 如果名字相同，索引变更(加了个字段什么的)，是不再处理的
     * @param collectionName 集合名字
     * @param indexName      索引名称
     * @param keys           索引字段及索引规则，如升序降序等
     */
    public void initIndex(String collectionName, String indexName, Bson keys) {
        initIndex(collectionName, indexName, keys, null);
    }
    
    /**
     * 初始化索引
     *   如果集合中没有名称为所给的索引则创建，如果已经存在则忽略
     *  *** 注意 如果名字相同，索引变更(加了个字段什么的)，是不再处理的
     *
     *  一些创建失败日志说明:
     *     name相同 key相同 options相同 可以重复调用
     *     其他都会报错，比如说(key相同，options不同) (key相同，name不同)，所以这里try掉
     *
     * @param collectionName 集合名字
     * @param indexName      索引名称 如果indexOptions中有name属性，会被该值覆盖
     * @param keys           索引字段及索引规则，如升序降序等
     * @param indexOptions   索引其他选项 比如唯一索引 过期等
     */
    public void initIndex(String collectionName, String indexName, Bson keys, IndexOptions indexOptions) {
        if (StringUtils.isBlank(indexName)) {
            throw new IllegalArgumentException("初始化索引必须提供name属性");
        }
        _initIndex(collectionName, indexName, keys, indexOptions);
    }
    
    /**
     * 删除索引
     * @param collectionName 集合名称
     * @param indexName 索引名称
     */
    public void dropIndex(String collectionName, String indexName) {
        dropIndex(collectionName, indexName, null);
    }
    
    /**
     * 删除索引
     * @param collectionName 集合名称
     * @param indexName 索引名称
     * @param dropIndexOptions 删除选项 我也不知道怎么用的
     */
    public void dropIndex(String collectionName, String indexName, DropIndexOptions dropIndexOptions) {
        if (StringUtils.isBlank(indexName)) {
            throw new IllegalArgumentException("删除索引必须提供name属性");
        }
        _dropIndex(collectionName, indexName, dropIndexOptions);
    }
    
    /**
     * 删除索引
     * @param collectionName 集合名称
     * @param keys 索引keys
     */
    public void dropIndex(String collectionName, Document keys) {
        dropIndex(collectionName, keys, null);
    }
    
    /**
     * 删除索引
     * @param collectionName 集合名称
     * @param keys 索引的keys
     * @param dropIndexOptions 删除选项 我也不知道怎么用的
     */
    public void dropIndex(String collectionName, Document keys, DropIndexOptions dropIndexOptions) {
        if (keys == null || keys.isEmpty()) {
            throw new IllegalArgumentException("删除索引必须keys属性");
        }
        _dropIndex(collectionName, keys, dropIndexOptions);
    }
    
    private final Queue<Runnable> queue = new ConcurrentLinkedQueue<>();
    
    /**
     * 初始化索引
     *   如果集合中没有名称为所给的索引则创建，如果已经存在则忽略
     *  *** 注意 如果名字相同，索引变更(加了个字段什么的)，是不再处理的
     *  一些创建失败日志说明:
     *     name相同 key相同 options相同 可以重复调用
     *     其他都会报错，比如说(key相同，options不同) (key相同，name不同)，所以这里try掉
     *
     * @param collectionName 集合名字
     * @param indexName      索引名称 如果indexOptions中有name属性，会被该值覆盖
     * @param keys           索引字段及索引规则，如升序降序等
     * @param indexOptions   索引其他选项 比如唯一索引 过期等
     */
    private void _initIndex(String collectionName, String indexName, Bson keys, IndexOptions indexOptions) {
        MongoCollection<Document> collection = this.getMongoDatabase().getCollection(collectionName);
        Map<String, Document> indexNameMapIndex = collection.listIndexes().into(new ArrayList<>()).stream()
                .collect(toMap(o -> o.getString("name"), o -> o));
        if (!indexNameMapIndex.containsKey(indexName)) {
            if (indexOptions == null) {
                indexOptions = new IndexOptions();
            }
            indexOptions.name(indexName);
            indexOptions.background(true);
            try {
                collection.createIndex(keys, indexOptions);
                log.debug("创建索引成功: {}-{};keys:{};", collectionName, indexName, keys);
            } catch (Exception e) {
                log.warn("创建索引失败: {}-{};keys:{}; causeBy: {} : {}",
                        collectionName,
                        indexName,
                        keys,
                        e.getClass().getName(),
                        e.getMessage()
                );
            }
        } else {
            log.debug("创建索引失败: {}-{};keys:{}: causeBy: 索引名已经存在", collectionName, indexName, keys);
        }
    }
    
    /**
     * 删除索引
     * @param collectionName 集合名称
     * @param indexName 索引名称
     * @param dropIndexOptions 删除选项 我也不知道怎么用的
     */
    private void _dropIndex(String collectionName, String indexName, DropIndexOptions dropIndexOptions) {
        MongoCollection<Document> collection = this.getMongoDatabase().getCollection(collectionName);
        Map<String, Document> indexNameMapIndex = collection.listIndexes().into(new ArrayList<>()).stream()
                .collect(toMap(o -> o.getString("name"), o -> o));
        if (indexNameMapIndex.containsKey(indexName)) {
            try {
                if (dropIndexOptions == null) {
                    collection.dropIndex(indexName);
                } else {
                    collection.dropIndex(indexName, dropIndexOptions);
                }
                log.debug("删除索引成功: {}-{}", collectionName, indexName);
            } catch (Exception e) {
                log.warn("删除索引失败: {}-{}; causeBy: {} : {}",
                        collectionName,
                        indexName,
                        e.getClass().getName(),
                        e.getMessage()
                );
            }
        } else {
            log.debug("删除索引失败: {}-{}; causeBy: 索引名不存在", collectionName, indexName);
        }
    }
    
    /**
     * 删除索引
     * @param collectionName 集合名称
     * @param keys 索引的keys
     * @param dropIndexOptions 删除选项 我也不知道怎么用的
     */
    private void _dropIndex(String collectionName, Document keys, DropIndexOptions dropIndexOptions) {
        MongoCollection<Document> collection = this.getMongoDatabase().getCollection(collectionName);
        List<Document> indexList = collection.listIndexes().into(new ArrayList<>());
        for (Document index : indexList) {
            Document keysExist = index.get("key", Document.class);
            if (keys.equals(keysExist)) {
                try {
                    if (dropIndexOptions == null) {
                        collection.dropIndex(keys);
                    } else {
                        collection.dropIndex(keys, dropIndexOptions);
                    }
                    log.debug("删除索引成功: {}-keys:{}", collectionName, keys);
                } catch (Exception e) {
                    log.warn("删除索引失败: {}-keys:{}; causeBy: {} : {}",
                            collectionName,
                            keys,
                            e.getClass().getName(),
                            e.getMessage()
                    );
                }
                return;
            }
        }
        log.debug("删除索引失败: {}-keys:{}; causeBy: 索引keys不存在", collectionName, keys);
    }
}
